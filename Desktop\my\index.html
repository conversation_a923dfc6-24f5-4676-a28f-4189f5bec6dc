<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>蓝洋林顿恺畅酒店 - 四星级豪华体验</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#1a56db',
            secondary: '#3b82f6',
            accent: '#f97316',
            dark: '#1e293b',
            light: '#f8fafc',
          },
          fontFamily: {
            inter: ['Inter', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .text-shadow {
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .transition-custom {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      .bg-blur {
        backdrop-filter: blur(8px);
      }
    }
  </style>
</head>
<body class="font-inter text-dark bg-light">
  <!-- 导航栏 -->
  <header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300 bg-transparent">
    <div class="container mx-auto px-4 py-4 flex justify-between items-center">
      <a href="#" class="flex items-center space-x-2">
        <span class="text-2xl font-bold text-white text-shadow-lg">蓝洋林顿恺畅酒店</span>
      </a>
      
      <!-- 桌面导航 -->
      <nav class="hidden md:flex space-x-8">
        <a href="#home" class="text-white hover:text-accent transition-custom font-medium">首页</a>
        <a href="#dining" class="text-white hover:text-accent transition-custom font-medium">餐饮</a>
        <a href="#facilities" class="text-white hover:text-accent transition-custom font-medium">设施</a>
        <a href="#offers" class="text-white hover:text-accent transition-custom font-medium">优惠</a>
        <a href="#contact" class="text-white hover:text-accent transition-custom font-medium">联系我们</a>
      </nav>
      
      <!-- 移动端菜单按钮 -->
      <button id="menu-toggle" class="md:hidden text-white text-2xl focus:outline-none">
        <i class="fa fa-bars"></i>
      </button>
    </div>
    
    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg absolute w-full">
      <div class="container mx-auto px-4 py-4 flex flex-col space-y-4">
        <a href="#home" class="text-dark hover:text-primary transition-custom font-medium py-2 border-b border-gray-100">首页</a>
        <a href="#dining" class="text-dark hover:text-primary transition-custom font-medium py-2 border-b border-gray-100">餐饮</a>
        <a href="#facilities" class="text-dark hover:text-primary transition-custom font-medium py-2 border-b border-gray-100">设施</a>
        <a href="#offers" class="text-dark hover:text-primary transition-custom font-medium py-2 border-b border-gray-100">优惠</a>
        <a href="#contact" class="text-dark hover:text-primary transition-custom font-medium py-2">联系我们</a>
      </div>
    </div>
  </header>

  <!-- 英雄区域 -->
  <section id="home" class="relative h-screen flex items-center justify-center overflow-hidden">
    <div class="absolute inset-0 z-0">
      <img src="https://picsum.photos/id/1059/1920/1080" alt="蓝洋林顿恺畅酒店餐厅" class="w-full h-full object-cover">
      <div class="absolute inset-0 bg-dark/60"></div>
    </div>
    
    <div class="container mx-auto px-4 z-10 text-center">
      <h1 class="text-[clamp(2.5rem,5vw,4rem)] font-bold text-white mb-6 leading-tight text-shadow-lg">
        蓝洋林顿恺畅酒店<br>四星级餐饮体验
      </h1>
      <p class="text-[clamp(1rem,2vw,1.25rem)] text-white/90 mb-10 max-w-2xl mx-auto text-shadow">
        位于城市中心的奢华酒店，为您提供无与伦比的美食体验和贴心服务
      </p>
      <div class="flex flex-col sm:flex-row justify-center gap-4">
        <a href="#booking" class="px-8 py-3 bg-accent hover:bg-accent/90 text-white font-medium rounded-lg shadow-lg transform hover:scale-105 transition-custom">
          立即预订
        </a>
        <a href="#dining" class="px-8 py-3 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-medium rounded-lg shadow-lg transform hover:scale-105 transition-custom">
          探索餐厅
        </a>
      </div>
    </div>
    
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
      <a href="#dining" class="flex flex-col items-center">
        <span class="mb-2 text-sm">向下滚动</span>
        <i class="fa fa-chevron-down"></i>
      </a>
    </div>
  </section>

  <!-- 预订区域 -->
  <section id="booking" class="py-12 bg-primary/5">
    <div class="container mx-auto px-4">
      <div class="bg-white rounded-xl shadow-xl p-8 -mt-20 relative z-10">
        <h3 class="text-2xl font-bold mb-6 text-center">立即预订您的完美用餐体验</h3>
        <form class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
          <div>
            <label for="reservation-date" class="block text-gray-700 mb-2">预订日期</label>
            <input type="date" id="reservation-date" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-custom">
          </div>
          <div>
            <label for="reservation-time" class="block text-gray-700 mb-2">预订时间</label>
            <select id="reservation-time" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-custom">
              <option value="">选择时间</option>
              <option value="11:30">11:30</option>
              <option value="12:00">12:00</option>
              <option value="12:30">12:30</option>
              <option value="13:00">13:00</option>
              <option value="13:30">13:30</option>
              <option value="18:00">18:00</option>
              <option value="18:30">18:30</option>
              <option value="19:00">19:00</option>
              <option value="19:30">19:30</option>
              <option value="20:00">20:00</option>
              <option value="20:30">20:30</option>
            </select>
          </div>
          <div>
            <label for="guests" class="block text-gray-700 mb-2">客人数量</label>
            <select id="guests" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-custom">
              <option value="1">1位客人</option>
              <option value="2" selected>2位客人</option>
              <option value="3">3位客人</option>
              <option value="4">4位客人</option>
              <option value="5">5位客人</option>
              <option value="6">6位客人</option>
              <option value="7">7位客人</option>
              <option value="8">8位客人</option>
            </select>
          </div>
          <div>
            <label for="restaurant" class="block text-gray-700 mb-2">选择餐厅</label>
            <select id="restaurant" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-custom">
              <option value="">选择餐厅</option>
              <option value="imperial">帝王餐厅</option>
              <option value="ocean">海景餐厅</option>
              <option value="garden">花园餐厅</option>
              <option value="fusion">融合餐厅</option>
            </select>
          </div>
          <div class="flex items-end lg:col-span-4">
            <button type="submit" class="w-full px-6 py-3 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg shadow-md transform hover:scale-[1.02] transition-custom">
              查询可用座位 <i class="fa fa-search ml-2"></i>
            </button>
          </div>
        </form>
      </div>
    </div>
  </section>

  <!-- 餐饮区域 -->
  <section id="dining" class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-[clamp(1.75rem,3vw,2.5rem)] font-bold text-dark mb-4">豪华餐饮体验</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">我们的餐厅提供各种国际美食，由顶级厨师精心烹制，为您带来味蕾的盛宴</p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
        <!-- 餐厅 1 -->
        <div class="bg-light rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-custom group">
          <div class="h-64 overflow-hidden">
            <img src="https://picsum.photos/id/292/800/600" alt="帝王餐厅" class="w-full h-full object-cover transform group-hover:scale-105 transition-custom duration-500">
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold mb-2 group-hover:text-primary transition-custom">帝王餐厅</h3>
            <div class="flex items-center mb-4">
              <div class="flex text-yellow-400 mr-2">
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
              </div>
              <span class="text-gray-600 text-sm">120条评价</span>
            </div>
            <p class="text-gray-600 mb-4">体验正宗的中式美食，由经验丰富的厨师主理，提供粤菜、川菜和淮扬菜等多种选择。</p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">中餐</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">包间</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">早午茶</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">私人定制</span>
            </div>
            <div class="flex justify-between items-center">
              <div>
                <span class="text-lg font-bold">人均消费:</span>
                <span class="text-gray-600">¥388起</span>
              </div>
              <a href="#booking" class="px-4 py-2 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg shadow-sm transform hover:scale-105 transition-custom">
                预订座位
              </a>
            </div>
          </div>
        </div>
        
        <!-- 餐厅 2 -->
        <div class="bg-light rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-custom group">
          <div class="h-64 overflow-hidden">
            <img src="https://picsum.photos/id/431/800/600" alt="海景餐厅" class="w-full h-full object-cover transform group-hover:scale-105 transition-custom duration-500">
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold mb-2 group-hover:text-primary transition-custom">海景餐厅</h3>
            <div class="flex items-center mb-4">
              <div class="flex text-yellow-400 mr-2">
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star-half-o"></i>
              </div>
              <span class="text-gray-600 text-sm">98条评价</span>
            </div>
            <p class="text-gray-600 mb-4">提供正宗的意大利和法国美食，新鲜的食材和精湛的烹饪技艺，让您领略地中海风情。</p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">西餐</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">海景</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">葡萄酒</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">浪漫氛围</span>
            </div>
            <div class="flex justify-between items-center">
              <div>
                <span class="text-lg font-bold">人均消费:</span>
                <span class="text-gray-600">¥488起</span>
              </div>
              <a href="#booking" class="px-4 py-2 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg shadow-sm transform hover:scale-105 transition-custom">
                预订座位
              </a>
            </div>
          </div>
        </div>
        
        <!-- 餐厅 3 -->
        <div class="bg-light rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-custom group">
          <div class="h-64 overflow-hidden">
            <img src="https://picsum.photos/id/287/800/600" alt="花园餐厅" class="w-full h-full object-cover transform group-hover:scale-105 transition-custom duration-500">
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold mb-2 group-hover:text-primary transition-custom">花园餐厅</h3>
            <div class="flex items-center mb-4">
              <div class="flex text-yellow-400 mr-2">
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star-o"></i>
              </div>
              <span class="text-gray-600 text-sm">76条评价</span>
            </div>
            <p class="text-gray-600 mb-4">置身于绿意盎然的花园中，品尝亚洲风味美食，提供泰国、日本和越南等特色菜肴。</p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">亚洲菜</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">户外</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">烧烤</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">素食选择</span>
            </div>
            <div class="flex justify-between items-center">
              <div>
                <span class="text-lg font-bold">人均消费:</span>
                <span class="text-gray-600">¥288起</span>
              </div>
              <a href="#booking" class="px-4 py-2 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg shadow-sm transform hover:scale-105 transition-custom">
                预订座位
              </a>
            </div>
          </div>
        </div>
        
        <!-- 餐厅 4 -->
        <div class="bg-light rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-custom group">
          <div class="h-64 overflow-hidden">
            <img src="https://picsum.photos/id/365/800/600" alt="融合餐厅" class="w-full h-full object-cover transform group-hover:scale-105 transition-custom duration-500">
          </div>
          <div class="p-6">
            <h3 class="text-xl font-bold mb-2 group-hover:text-primary transition-custom">融合餐厅</h3>
            <div class="flex items-center mb-4">
              <div class="flex text-yellow-400 mr-2">
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star"></i>
                <i class="fa fa-star-half-o"></i>
              </div>
              <span class="text-gray-600 text-sm">65条评价</span>
            </div>
            <p class="text-gray-600 mb-4">创新融合料理，将不同国家的烹饪风格巧妙结合，为您带来全新的味觉体验。</p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">创意菜</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">小份菜</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">鸡尾酒</span>
              <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">夜间娱乐</span>
            </div>
            <div class="flex justify-between items-center">
              <div>
                <span class="text-lg font-bold">人均消费:</span>
                <span class="text-gray-600">¥368起</span>
              </div>
              <a href="#booking" class="px-4 py-2 bg-accent hover:bg-accent/90 text-white font-medium rounded-lg shadow-sm transform hover:scale-105 transition-custom">
                预订座位
              </a>
            </div>
          </div>
        </div>
      </div>
      
      <div class="text-center mt-12">
        <a href="#" class="inline-block px-8 py-3 bg-white border border-primary text-primary hover:bg-primary/5 font-medium rounded-lg shadow-sm transform hover:scale-105 transition-custom">
          查看全部餐厅 <i class="fa fa-long-arrow-right ml-2"></i>
        </a>
      </div>
    </div>
  </section>

  <!-- 设施区域 -->
  <section id="facilities" class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-[clamp(1.75rem,3vw,2.5rem)] font-bold text-dark mb-4">酒店设施</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">我们提供各种高端设施，确保您在酒店的每一刻都舒适愉快</p>
      </div>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 设施 1 -->
        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-custom text-center group">
          <div class="w-16 h-16 bg-primary/10 text-primary rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary group-hover:text-white transition-custom">
            <i class="fa fa-tint text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3">游泳池</h3>
          <p class="text-gray-600">室内恒温游泳池，配备专业救生员，让您随时享受游泳乐趣。</p>
        </div>
        
        <!-- 设施 2 -->
        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-custom text-center group">
          <div class="w-16 h-16 bg-primary/10 text-primary rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary group-hover:text-white transition-custom">
            <i class="fa fa-heartbeat text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3">健身中心</h3>
          <p class="text-gray-600">24小时开放的现代化健身中心，配备顶级器材和专业教练。</p>
        </div>
        
        <!-- 设施 3 -->
        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-custom text-center group">
          <div class="w-16 h-16 bg-primary/10 text-primary rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary group-hover:text-white transition-custom">
            <i class="fa fa-towel text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3">水疗中心</h3>
          <p class="text-gray-600">提供各种按摩和护理服务，让您在繁忙的旅行中放松身心。</p>
        </div>
        
        <!-- 设施 4 -->
        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-custom text-center group">
          <div class="w-16 h-16 bg-primary/10 text-primary rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary group-hover:text-white transition-custom">
            <i class="fa fa-wifi text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3">免费WiFi</h3>
          <p class="text-gray-600">全酒店覆盖高速WiFi，让您随时保持与外界的联系。</p>
        </div>
      </div>
    </div>
  </section>

  <!-- 优惠活动 -->
  <section id="offers" class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-[clamp(1.75rem,3vw,2.5rem)] font-bold text-dark mb-4">特别优惠</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">探索我们的特别优惠，为您的下一次用餐体验节省更多</p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- 优惠 1 -->
        <div class="bg-light rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-custom group">
          <div class="h-48 overflow-hidden">
            <img src="https://picsum.photos/id/292/800/600" alt="双人晚餐套餐" class="w-full h-full object-cover transform group-hover:scale-105 transition-custom duration-500">
          </div>
          <div class="p-6">
            <div class="flex justify-between items-start mb-4">
              <h3 class="text-xl font-bold group-hover:text-primary transition-custom">双人晚餐套餐</h3>
              <span class="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium">-20%</span>
            </div>
            <p class="text-gray-600 mb-4">在帝王餐厅享受双人套餐，包含前菜、主菜、甜点和两杯精选葡萄酒。</p>
            <div class="flex items-center text-gray-600 mb-4">
              <i class="fa fa-calendar mr-2"></i>
              <span>有效期至: 2025年8月31日</span>
            </div>
            <div class="flex justify-between items-center">
              <div>
                <span class="text-2xl font-bold">¥599</span>
                <span class="text-gray-600 line-through ml-2">¥749</span>
              </div>
              <a href="#booking" class="px-4 py-2 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg shadow-sm transform hover:scale-105 transition-custom">
                立即预订
              </a>
            </div>
          </div>
        </div>
        
        <!-- 优惠 2 -->
        <div class="bg-light rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-custom group">
          <div class="h-48 overflow-hidden">
            <img src="https://picsum.photos/id/431/800/600" alt="周末早午餐" class="w-full h-full object-cover transform group-hover:scale-105 transition-custom duration-500">
          </div>
          <div class="p-6">
            <div class="flex justify-between items-start mb-4">
              <h3 class="text-xl font-bold group-hover:text-primary transition-custom">周末早午餐</h3>
              <span class="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium">-15%</span>
            </div>
            <p class="text-gray-600 mb-4">在海景餐厅享受丰盛的周末早午餐，包含各式美食和无限畅饮咖啡/茶。</p>
            <div class="flex items-center text-gray-600 mb-4">
              <i class="fa fa-calendar mr-2"></i>
              <span>每周六、日: 10:00-14:00</span>
            </div>
            <div class="flex justify-between items-center">
              <div>
                <span class="text-2xl font-bold">¥198</span>
                <span class="text-gray-600 line-through ml-2">¥233</span>
              </div>
              <a href="#booking" class="px-4 py-2 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg shadow-sm transform hover:scale-105 transition-custom">
                立即预订
              </a>
            </div>
          </div>
        </div>
        
        <!-- 优惠 3 -->
        <div class="bg-light rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-custom group">
          <div class="h-48 overflow-hidden">
            <img src="https://picsum.photos/id/287/800/600" alt="商务午餐套餐" class="w-full h-full object-cover transform group-hover:scale-105 transition-custom duration-500">
          </div>
          <div class="p-6">
            <div class="flex justify-between items-start mb-4">
              <h3 class="text-xl font-bold group-hover:text-primary transition-custom">商务午餐套餐</h3>
              <span class="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium">-25%</span>
            </div>
            <p class="text-gray-600 mb-4">工作日商务午餐套餐，快速服务，包含前菜、主菜和甜点，提升工作效率。</p>
            <div class="flex items-center text-gray-600 mb-4">
              <i class="fa fa-calendar mr-2"></i>
              <span>周一至周五: 11:30-14:30</span>
            </div>
            <div class="flex justify-between items-center">
              <div>
                <span class="text-2xl font-bold">¥128</span>
                <span class="text-gray-600 line-through ml-2">¥170</span>
              </div>
              <a href="#booking" class="px-4 py-2 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg shadow-sm transform hover:scale-105 transition-custom">
                立即预订
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 客户评价 -->
  <section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="text-[clamp(1.75rem,3vw,2.5rem)] font-bold text-dark mb-4">客户评价</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">听听我们的客人对蓝洋林顿恺畅酒店的真实评价</p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- 评价 1 -->
        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-custom">
          <div class="flex text-yellow-400 mb-4">
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
          </div>
          <p class="text-gray-600 mb-6">"帝王餐厅的服务令人印象深刻，服务员非常专业且友好。食物的质量超出了我的期望，特别是他们的招牌烤鸭，皮脆肉嫩，非常美味。"</p>
          <div class="flex items-center">
            <img src="https://picsum.photos/id/1005/100/100" alt="张先生" class="w-12 h-12 rounded-full object-cover mr-4">
            <div>
              <h4 class="font-bold">张先生</h4>
              <p class="text-gray-500 text-sm">商务旅客</p>
            </div>
          </div>
        </div>
        
        <!-- 评价 2 -->
        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-custom">
          <div class="flex text-yellow-400 mb-4">
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
          </div>
          <p class="text-gray-600 mb-6">"海景餐厅的氛围非常浪漫，我们在这里庆祝了结婚周年纪念日。服务员为我们安排了靠窗的位置，让我们可以欣赏美丽的夜景。食物和葡萄酒都非常出色。"</p>
          <div class="flex items-center">
            <img src="https://picsum.photos/id/1006/100/100" alt="李女士" class="w-12 h-12 rounded-full object-cover mr-4">
            <div>
              <h4 class="font-bold">李女士</h4>
              <p class="text-gray-500 text-sm">休闲旅客</p>
            </div>
          </div>
        </div>
        
        <!-- 评价 3 -->
        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-custom">
          <div class="flex text-yellow-400 mb-4">
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
            <i class="fa fa-star"></i>
            <i class="fa fa-star-half-o"></i>
          </div>
          <p class="text-gray-600 mb-6">"花园餐厅的环境非常舒适，户外座位让我可以享受新鲜空气和自然景色。融合餐厅的创意菜很有特色，每道菜都有独特的口味和呈现方式。"</p>
          <div class="flex items-center">
            <img src="https://picsum.photos/id/1012/100/100" alt="王先生" class="w-12 h-12 rounded-full object-cover mr-4">
            <div>
              <h4 class="font-bold">王先生</h4>
              <p class="text-gray-500 text-sm">本地居民</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 联系我们 -->
  <section id="contact" class="py-20 bg-white">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <div>
          <h2 class="text-[clamp(1.75rem,3vw,2.5rem)] font-bold text-dark mb-6">联系我们</h2>
          <p class="text-gray-600 mb-8">无论您有任何问题或需求，我们的团队随时为您提供帮助。欢迎通过以下方式与我们联系。</p>
          
          <div class="space-y-6">
            <div class="flex items-start">
              <div class="w-12 h-12 bg-primary/10 text-primary rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                <i class="fa fa-map-marker"></i>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-1">地址</h3>
                <p class="text-gray-600">上海市浦东新区陆家嘴环路1288号</p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="w-12 h-12 bg-primary/10 text-primary rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                <i class="fa fa-phone"></i>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-1">电话</h3>
                <p class="text-gray-600">+86 21 5888 8888</p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="w-12 h-12 bg-primary/10 text-primary rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                <i class="fa fa-envelope"></i>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-1">邮箱</h3>
                <p class="text-gray-600"><EMAIL></p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="w-12 h-12 bg-primary/10 text-primary rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                <i class="fa fa-clock-o"></i>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-1">营业时间</h3>
                <p class="text-gray-600">帝王餐厅: 11:00 - 22:00</p>
                <p class="text-gray-600">海景餐厅: 11:30 - 23:00</p>
                <p class="text-gray-600">花园餐厅: 10:00 - 22:30</p>
                <p class="text-gray-600">融合餐厅: 17:30 - 24:00</p>
              </div>
            </div>
          </div>
          
          <div class="mt-10">
            <h3 class="font-bold text-lg mb-4">关注我们</h3>
            <div class="flex space-x-4">
              <a href="#" class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-primary/90 transition-custom">
                <i class="fa fa-weixin"></i>
              </a>
              <a href="#" class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-primary/90 transition-custom">
                <i class="fa fa-weibo"></i>
              </a>
              <a href="#" class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-primary/90 transition-custom">
                <i class="fa fa-instagram"></i>
              </a>
              <a href="#" class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-primary/90 transition-custom">
                <i class="fa fa-facebook"></i>
              </a>
            </div>
          </div>
        </div>
        
        <div>
          <div class="bg-light rounded-xl p-8 shadow-md">
            <h3 class="text-xl font-bold mb-6">发送消息</h3>
            <form>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label for="name" class="block text-gray-700 mb-2">姓名</label>
                  <input type="text" id="name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-custom" placeholder="您的姓名">
                </div>
                <div>
                  <label for="email" class="block text-gray-700 mb-2">邮箱</label>
                  <input type="email" id="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-custom" placeholder="您的邮箱">
                </div>
              </div>
              
              <div class="mb-6">
                <label for="subject" class="block text-gray-700 mb-2">主题</label>
                <input type="text" id="subject" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-custom" placeholder="消息主题">
              </div>
              
              <div class="mb-6">
                <label for="message" class="block text-gray-700 mb-2">消息</label>
                <textarea id="message" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-custom" placeholder="请输入您的消息..."></textarea>
              </div>
              
              <button type="submit" class="w-full px-6 py-3 bg-primary hover:bg-primary/90 text-white font-medium rounded-lg shadow-md transform hover:scale-[1.02] transition-custom">
                发送消息 <i class="fa fa-paper-plane ml-2"></i>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 页脚 -->
  <footer class="bg-dark text-white py-16">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
        <div>
          <h3 class="text-2xl font-bold mb-6">蓝洋林顿恺畅酒店</h3>
          <p class="text-gray-400 mb-6">位于城市中心的奢华酒店，为您提供无与伦比的餐饮体验和贴心服务。</p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-white transition-custom">
              <i class="fa fa-weixin text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-custom">
              <i class="fa fa-weibo text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-custom">
              <i class="fa fa-instagram text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-custom">
              <i class="fa fa-facebook text-xl"></i>
            </a>
          </div>
        </div>
        
        <div>
          <h4 class="text-lg font-bold mb-6">快速链接</h4>
          <ul class="space-y-3">
            <li><a href="#home" class="text-gray-400 hover:text-white transition-custom">首页</a></li>
            <li><a href="#dining" class="text-gray-400 hover:text-white transition-custom">餐饮</a></li>
            <li><a href="#facilities" class="text-gray-400 hover:text-white transition-custom">设施</a></li>
            <li><a href="#offers" class="text-gray-400 hover:text-white transition-custom">优惠</a></li>
            <li><a href="#contact" class="text-gray-400 hover:text-white transition-custom">联系我们</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="text-lg font-bold mb-6">餐厅</h4>
          <ul class="space-y-3">
            <li><a href="#" class="text-gray-400 hover:text-white transition-custom">帝王餐厅</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-custom">海景餐厅</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-custom">花园餐厅</a></li>
            <li><a href="#" class="text-gray-400 hover:text-white transition-custom">融合餐厅</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="text-lg font-bold mb-6">联系信息</h4>
          <ul class="space-y-3">
            <li class="flex items-start">
              <i class="fa fa-map-marker text-gray-400 mr-3 mt-1"></i>
              <span class="text-gray-400">上海市浦东新区陆家嘴环路1288号</span>
            </li>
            <li class="flex items-start">
              <i class="fa fa-phone text-gray-400 mr-3 mt-1"></i>
              <span class="text-gray-400">+86 21 5888 8888</span>
            </li>
            <li class="flex items-start">
              <i class="fa fa-envelope text-gray-400 mr-3 mt-1"></i>
              <span class="text-gray-400"><EMAIL></span>
            </li>
          </ul>
        </div>
      </div>
      
      <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-500 mb-4 md:mb-0">© 2025 蓝洋林顿恺畅酒店. 保留所有权利.</p>
        <div class="flex space-x-6">
          <a href="#" class="text-gray-500 hover:text-white transition-custom">隐私政策</a>
          <a href="#" class="text-gray-500 hover:text-white transition-custom">使用条款</a>
          <a href="#" class="text-gray-500 hover:text-white transition-custom">网站地图</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- 返回顶部按钮 -->
  <button id="back-to-top" class="fixed bottom-8 right-8 bg-primary text-white w-12 h-12 rounded-full flex items-center justify-center shadow-lg transform hover:scale-110 transition-custom opacity-0 invisible">
    <i class="fa fa-chevron-up"></i>
  </button>

  <!-- JavaScript -->
  <script>
    // 导航栏滚动效果
    const navbar = document.getElementById('navbar');
    const backToTopBtn = document.getElementById('back-to-top');
    
    window.addEventListener('scroll', function() {
      if (window.scrollY > 50) {
        navbar.classList.add('bg-white', 'shadow-md');
        navbar.classList.remove('bg-transparent');
        document.querySelectorAll('#navbar a').forEach(link => {
          link.classList.remove('text-white');
          link.classList.add('text-dark');
        });
        document.getElementById('menu-toggle').classList.remove('text-white');
        document.getElementById('menu-toggle').classList.add('text-dark');
        
        backToTopBtn.classList.remove('opacity-0', 'invisible');
        backToTopBtn.classList.add('opacity-100', 'visible');
      } else {
        navbar.classList.remove('bg-white', 'shadow-md');
        navbar.classList.add('bg-transparent');
        document.querySelectorAll('#navbar a').forEach(link => {
          link.classList.add('text-white');
          link.classList.remove('text-dark');
        });
        document.getElementById('menu-toggle').classList.add('text-white');
        document.getElementById('menu-toggle').classList.remove('text-dark');
        
        backToTopBtn.classList.add('opacity-0', 'invisible');
        backToTopBtn.classList.remove('opacity-100', 'visible');
      }
    });
    
    // 移动端菜单切换
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    
    menuToggle.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
      if (mobileMenu.classList.contains('hidden')) {
        menuToggle.innerHTML = '<i class="fa fa-bars"></i>';
      } else {
        menuToggle.innerHTML = '<i class="fa fa-times"></i>';
      }
    });
    
    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        
        // 关闭移动菜单
        if (!mobileMenu.classList.contains('hidden')) {
          mobileMenu.classList.add('hidden');
          menuToggle.innerHTML = '<i class="fa fa-bars"></i>';
        }
        
        const targetId = this.getAttribute('href');
        if (targetId === '#') return;
        
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
          window.scrollTo({
            top: targetElement.offsetTop - 80,
            behavior: 'smooth'
          });
        }
      });
    });
    
    // 返回顶部按钮
    backToTopBtn.addEventListener('click', function() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
    
    // 设置日期选择器的最小值为今天
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('check-in').min = today;
    document.getElementById('reservation-date').min = today;
    
    // 入住日期变化时，更新退房日期的最小值
    document.getElementById('check-in').addEventListener('change', function() {
      document.getElementById('check-out').min = this.value;
    });
  </script>
</body>
</html>
  